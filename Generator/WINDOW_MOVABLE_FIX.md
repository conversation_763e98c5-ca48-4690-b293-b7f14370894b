# 🪟 Oprava presúvania okna aplikácie

## ❌ Problém
Aplikácia bola fixná na obrazovke a nedala sa presúvať ani meniť veľkosť.

## ✅ Riešenie implementované

### 1. **Aktualizované nastavenia BrowserWindow**
```javascript
mainWindow = new BrowserWindow({
  width: 1400,
  height: 900,
  minWidth: 800,
  minHeight: 600,
  movable: true,        // ✅ Explicitne povoliť presúvanie
  resizable: true,      // ✅ Povoliť zmenu veľkosti
  minimizable: true,    // ✅ Povoliť minimalizáciu
  maximizable: true,    // ✅ Povoliť maximalizáciu
  closable: true,       // ✅ Povoliť zatvorenie
  titleBarStyle: 'default', // ✅ Štandardný title bar
  frame: true           // ✅ Zabezpečiť rám okna
});
```

### 2. **Dodatočné kontroly po načítaní**
```javascript
mainWindow.once('ready-to-show', () => {
  mainWindow.show();
  
  // Dodatočné zabezpečenie presúvateľnosti
  mainWindow.setMovable(true);
  mainWindow.setResizable(true);
});
```

### 3. **Aktualizovaná ikonka**
- Zmenené z `assets/logo.jpg` na `assets/icon.png`
- Používa nové logo eSpomienka

## 🔧 Technické detaily

### Pôvodný problém:
- `titleBarStyle: 'hiddenInset'` na macOS mohol blokovať presúvanie
- Chýbali explicitné nastavenia `movable: true`
- Neboli nastavené základné vlastnosti okna

### Riešenie:
1. **Explicitné nastavenia**: Všetky vlastnosti okna sú teraz explicitne nastavené
2. **Štandardný title bar**: Používa `titleBarStyle: 'default'` pre lepšiu kompatibilitu
3. **Dodatočné kontroly**: Po načítaní sa ešte raz zabezpečí presúvateľnosť
4. **Plný rám okna**: `frame: true` zabezpečuje správny title bar

## 🎯 Výsledok

### ✅ Aplikácia je teraz plne funkčná:
- **Presúvanie**: Dá sa presúvať po celej obrazovke
- **Zmena veľkosti**: Dá sa meniť veľkosť ťahaním za okraje
- **Minimalizácia**: Dá sa minimalizovať do docku/taskbaru
- **Maximalizácia**: Dá sa maximalizovať na celú obrazovku
- **Zatvorenie**: Dá sa normálne zatvoriť

### 🖱️ Ako používať:
1. **Presúvanie**: Uchopte title bar a ťahajte
2. **Zmena veľkosti**: Ťahajte za okraje alebo rohy okna
3. **Minimalizácia**: Kliknite na žlté tlačidlo (macOS) alebo minimize tlačidlo
4. **Maximalizácia**: Kliknite na zelené tlačidlo (macOS) alebo maximize tlačidlo

## 📝 Poznámky

- Zmeny sú kompatibilné so všetkými platformami (macOS, Windows, Linux)
- Zachováva sa pôvodná funkcionalita aplikácie
- Žiadne zmeny v UI alebo používateľskom rozhraní
- Aplikácia si pamätá posledné nastavenia okna

## 🚀 Ďalšie kroky

Pre vytvorenie novej verzie s týmito opravami spustite:
```bash
npm run build
```

Nová verzia bude mať:
- ✅ Presúvateľné okno
- ✅ Nové logo ako ikonku
- ✅ Všetky predchádzajúce funkcie
