/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #f8f9fa;
    -webkit-app-region: no-drag; /* Ensure body doesn't interfere with dragging */
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

/* Main Navigation */
.main-nav {
    display: flex;
    gap: 0.5rem;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 60px;
    width: auto;
    max-width: 80px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: contain; /* Prevents logo squishing */
    filter: brightness(0) invert(1); /* Makes logo white */
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-icon {
    font-size: 2.5rem;
    color: #327881;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: white; /* White text on purple background */
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-left: 0.5rem;
    color: white; /* White subtitle on purple background */
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(94, 46, 96, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-generate {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1rem;
}

/* Main Content Layout */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Tab System */
.tab-content {
    width: 100%;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.quotes-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
}

.orders-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-layout {
    max-width: 800px;
}

/* Sidebar Navigation */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.service-nav h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    transform: translateX(4px);
}

.nav-link i {
    width: 16px;
    text-align: center;
}

/* Form Area */
.form-area {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Customer Section */
.customer-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
}

.customer-section h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group input:required:invalid {
    border-color: #e74c3c;
}

/* Services Section */
.services-section {
    margin-top: 2rem;
}

.service-category {
    margin-bottom: 3rem;
    display: none;
}

.service-category.active {
    display: block;
}

.service-category h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    gap: 2rem;
}

.service-subcategory {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #327881;
}

.service-subcategory h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-item {
    margin-bottom: 1rem;
    position: relative;
}

.service-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.service-label:hover {
    border-color: #327881;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d0d0d0;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.service-label input[type="checkbox"]:checked + .checkmark {
    background: #5e2e60;
    border-color: #5e2e60;
}

.service-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.service-price {
    font-weight: 600;
    color: #327881;
    font-size: 1rem;
}

.service-label input[type="checkbox"]:checked ~ .service-name {
    color: #5e2e60;
}

.service-label input[type="checkbox"]:checked {
    border-color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Custom Input Fields */
.custom-input {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f0f0;
    border-radius: 6px;
    display: none;
}

.service-item:has(input[type="checkbox"]:checked) .custom-input {
    display: block;
}

.custom-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Calculator Sidebar */
.calculator {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.calculator-content h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-services {
    margin-bottom: 1.5rem;
}

.selected-services h4 {
    color: #666;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-services {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.selected-service {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.selected-service:last-child {
    border-bottom: none;
}

.service-name-calc {
    flex: 1;
    color: #333;
}

.service-price-calc {
    color: #327881;
    font-weight: 600;
}

.price-summary {
    border-top: 2px solid #f0f0f0;
    padding-top: 1rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.price-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 1px solid #e0e0e0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 280px;
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .sidebar,
    .calculator {
        position: static;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .form-area,
    .sidebar,
    .calculator {
        padding: 1rem;
    }
    
    .logo-section {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .subtitle {
        margin-left: 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Dashboard Styles */
.dashboard {
    margin-bottom: 2rem;
}

.dashboard h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #327881;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-icon {
    font-size: 2rem;
    color: #5e2e60;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(94, 46, 96, 0.1);
    border-radius: 50%;
}

.dashboard-card .card-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #5e2e60;
    margin: 0;
}

.dashboard-card .card-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Orders Management */
.orders-management {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.orders-header h3 {
    color: #5e2e60;
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.orders-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Tasks */
.tasks-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tasks-section h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
    opacity: 0.7;
    background: #f8f9fa;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.task-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.task-detail {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-actions .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #5e2e60;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

/* Calendar Styles */
.calendar-container {
    width: 100%;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-header h4 {
    margin: 0;
    color: #5e2e60;
    font-size: 1.2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #999;
}

.calendar-day.today {
    background: rgba(94, 46, 96, 0.1);
    border: 2px solid #5e2e60;
}

.calendar-day.has-tasks {
    background: rgba(50, 120, 129, 0.1);
}

.calendar-day-number {
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-task {
    background: #327881;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* Settings Styles */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: #333;
}

.setting-item input,
.setting-item select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    min-width: 200px;
}

.setting-item input[type="checkbox"] {
    min-width: auto;
    width: 20px;
    height: 20px;
}

/* Enhanced Task Styles */
.task-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.task-item:hover {
    border-color: #327881;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.task-item.task-pending {
    border-left: 4px solid #ffc107;
}

.task-item.task-in_progress {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.task-item.task-completed {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.task-item.task-cancelled {
    border-left: 4px solid #dc3545;
    opacity: 0.7;
}

.task-status-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.status-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #327881 0%, #5e2e60 100%);
    transition: width 0.3s ease;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.task-status {
    background: #f8f9fa;
    color: #5e2e60;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.task-progress-steps {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.step.completed {
    color: #28a745;
    font-weight: 500;
}

.step i {
    width: 20px;
    text-align: center;
}

/* Task Progress Modal Styles */
.task-progress-content {
    max-width: 600px;
}

.task-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.task-info h4 {
    color: #5e2e60;
    margin-bottom: 0.5rem;
}

.task-info p {
    margin: 0.25rem 0;
    color: #666;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.step-item.active {
    border-color: #327881;
    background: rgba(50, 120, 129, 0.05);
}

.step-item.completed {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #666;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-item.active .step-icon {
    background: #327881;
    color: white;
}

.step-item.completed .step-icon {
    background: #28a745;
    color: white;
}

.step-content {
    flex: 1;
}

.step-content h5 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.step-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.completed-time {
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
}

.completion-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e0e0e0;
}

.completion-section h5 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-section textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-family: inherit;
    resize: vertical;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
}

/* Completion Modal Styles */
.completion-summary {
    text-align: center;
    max-width: 500px;
}

.completion-header {
    margin-bottom: 2rem;
}

.completion-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.completion-details {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: left;
}

.completion-details h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
}

.completion-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.completion-notes p {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    font-style: italic;
}

.photo-summary {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.photo-summary span {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.completion-actions {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

/* Photo Viewer Styles */
.photos-viewer h4 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    text-align: center;
}

.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.photo-item {
    text-align: center;
}

.photo-preview {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.photo-timestamp {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-info {
    border-left: 4px solid #007bff;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-content i {
    color: #28a745;
}

.notification-info .notification-content i {
    color: #007bff;
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
