// Orders Management System
// Data structures and functionality for managing orders and tasks

// Global data storage
let orders = [];
let tasks = [];
let currentDate = new Date();

// Package definitions for automatic task generation
const packageDefinitions = {
    sviatocny_urnove: {
        name: '<PERSON><PERSON><PERSON><PERSON>n<PERSON> - Urnové miesto',
        price: 90,
        tasks: [
            { offset: 30, type: 'základ<PERSON>', description: 'Základná údržba' },
            { date: 'easter', type: 'základ<PERSON>', description: 'Veľkonočné čistenie' },
            { date: '11-01', type: 'základ<PERSON>', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základ<PERSON>', description: 'Vianočné čistenie' }
        ]
    },
    sviatocny_jednohrob: {
        name: 'Sviatočný - Jednohrob',
        price: 208,
        tasks: [
            { offset: 30, type: 'základ<PERSON>', description: '<PERSON><PERSON><PERSON><PERSON> údržba' },
            { date: 'easter', type: 'z<PERSON>lad<PERSON>', description: 'Veľkonočn<PERSON> čistenie' },
            { date: '11-01', type: 'základ<PERSON>', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
        ]
    },
    sviatocny_dvojhrob: {
        name: 'Sviatočný - Dvojhrob',
        price: 300,
        tasks: [
            { offset: 30, type: 'základná', description: 'Základná údržba' },
            { date: 'easter', type: 'základná', description: 'Veľkonočné čistenie' },
            { date: '11-01', type: 'základná', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
        ]
    },
    celorocny_urnove: {
        name: 'Celoročný Premium - Urnové miesto',
        price: 354,
        tasks: 'monthly' // Generate monthly tasks
    },
    celorocny_jednohrob: {
        name: 'Celoročný Premium - Jednohrob',
        price: 806,
        tasks: 'monthly'
    },
    celorocny_dvojhrob: {
        name: 'Celoročný Premium - Dvojhrob',
        price: 1036,
        tasks: 'monthly'
    }
};

// Initialize orders management
document.addEventListener('DOMContentLoaded', function() {
    initializeOrdersSystem();
    loadOrdersData();
    updateDashboard();
    updateTodayDate();
});

function initializeOrdersSystem() {
    // Tab navigation
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Add order form
    const addOrderForm = document.getElementById('addOrderForm');
    if (addOrderForm) {
        addOrderForm.addEventListener('submit', handleAddOrder);
    }

    // Filters
    const filters = ['searchTasks', 'dateFilter', 'locationFilter', 'typeFilter'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', filterTasks);
            element.addEventListener('input', filterTasks);
        }
    });
}

function switchTab(tabName) {
    // Update nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // Update dashboard if switching to orders
    if (tabName === 'orders') {
        updateDashboard();
        renderTasks();
    }
}

function updateTodayDate() {
    const today = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    const todayElement = document.getElementById('todayDate');
    if (todayElement) {
        todayElement.textContent = today.toLocaleDateString('sk-SK', options);
    }
}

function updateDashboard() {
    const activeContracts = orders.filter(order => order.status === 'active').length;
    const monthlyCleanings = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const now = new Date();
        return taskDate.getMonth() === now.getMonth() && 
               taskDate.getFullYear() === now.getFullYear();
    }).length;
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const completedToday = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        return task.status === 'completed' &&
               taskDate.toDateString() === today.toDateString();
    }).length;

    // Update dashboard cards
    updateDashboardCard('activeContracts', activeContracts);
    updateDashboardCard('monthlyCleanings', monthlyCleanings);
    updateDashboardCard('pendingTasks', pendingTasks);
    updateDashboardCard('completedToday', completedToday);
}

function updateDashboardCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

function generateOrderId() {
    return 'ORD-' + Date.now().toString().slice(-6);
}

function generateTaskId() {
    return 'TASK-' + Date.now().toString().slice(-6) + '-' + Math.random().toString(36).substr(2, 3);
}

function calculateEasterDate(year) {
    // Easter calculation algorithm
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;
    return new Date(year, month - 1, day);
}

function generateTasks(packageType, startDate, orderId) {
    const packageDef = packageDefinitions[packageType];
    if (!packageDef) return [];

    const generatedTasks = [];
    const start = new Date(startDate);
    const year = start.getFullYear();

    if (packageDef.tasks === 'monthly') {
        // Generate monthly tasks for a year
        for (let i = 0; i < 12; i++) {
            const taskDate = new Date(start);
            taskDate.setMonth(start.getMonth() + i);
            
            generatedTasks.push({
                id: generateTaskId(),
                orderId: orderId,
                date: taskDate.toISOString().split('T')[0],
                type: 'základná',
                description: 'Mesačná údržba',
                status: 'pending',
                notes: '',
                photos: []
            });
        }
    } else {
        // Generate specific tasks
        packageDef.tasks.forEach(taskDef => {
            let taskDate;
            
            if (taskDef.offset) {
                taskDate = new Date(start);
                taskDate.setDate(taskDate.getDate() + taskDef.offset);
            } else if (taskDef.date === 'easter') {
                taskDate = calculateEasterDate(year);
            } else {
                const [month, day] = taskDef.date.split('-');
                taskDate = new Date(year, parseInt(month) - 1, parseInt(day));
            }

            generatedTasks.push({
                id: generateTaskId(),
                orderId: orderId,
                date: taskDate.toISOString().split('T')[0],
                type: taskDef.type,
                description: taskDef.description,
                status: 'pending',
                notes: '',
                photos: []
            });
        });
    }

    return generatedTasks;
}

function createOrderFromQuote() {
    if (selectedServices.length === 0) {
        alert('Najprv vyberte služby v cenovej ponuke.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Najprv vyplňte údaje o zákazníkovi.');
        return;
    }

    // Switch to orders tab and show add order modal with pre-filled data
    switchTab('orders');
    
    // Pre-fill the form with customer data
    document.getElementById('orderCustomerName').value = customerData.name || '';
    document.getElementById('orderCustomerPhone').value = customerData.phone || '';
    document.getElementById('orderCustomerEmail').value = customerData.email || '';
    
    // Set default start date to today
    document.getElementById('orderStartDate').value = new Date().toISOString().split('T')[0];
    
    showAddOrderModal();
}

function showAddOrderModal() {
    const modal = document.getElementById('addOrderModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
    modal.style.display = 'none';
}

function handleAddOrder(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const orderData = {
        id: generateOrderId(),
        customer: {
            name: document.getElementById('orderCustomerName').value,
            phone: document.getElementById('orderCustomerPhone').value,
            email: document.getElementById('orderCustomerEmail').value
        },
        location: document.getElementById('orderLocation').value,
        package: document.getElementById('orderPackage').value,
        startDate: document.getElementById('orderStartDate').value,
        endDate: calculateEndDate(document.getElementById('orderStartDate').value),
        status: 'active',
        createdAt: new Date().toISOString()
    };

    // Generate tasks for this order
    const generatedTasks = generateTasks(orderData.package, orderData.startDate, orderData.id);
    
    // Add to storage
    orders.push(orderData);
    tasks.push(...generatedTasks);
    
    // Save to localStorage
    saveOrdersData();
    
    // Update UI
    updateDashboard();
    renderTasks();
    
    // Close modal
    closeModal('addOrderModal');
    
    // Reset form
    e.target.reset();
    
    alert(`Objednávka ${orderData.id} bola úspešne vytvorená s ${generatedTasks.length} úlohami.`);
}

function calculateEndDate(startDate) {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setFullYear(end.getFullYear() + 1);
    return end.toISOString().split('T')[0];
}

function saveOrdersData() {
    localStorage.setItem('orders', JSON.stringify(orders));
    localStorage.setItem('tasks', JSON.stringify(tasks));
}

function loadOrdersData() {
    const savedOrders = localStorage.getItem('orders');
    const savedTasks = localStorage.getItem('tasks');

    if (savedOrders) {
        orders = JSON.parse(savedOrders);
    }

    if (savedTasks) {
        tasks = JSON.parse(savedTasks);
    }
}

function renderTasks() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Filter tasks for today
    const todayTasks = tasks.filter(task => task.date === todayStr);

    // Filter upcoming tasks (next 7 days)
    const upcomingTasks = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const diffTime = taskDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 && diffDays <= 7;
    });

    // Render today's tasks
    renderTasksList('todayTasks', todayTasks);

    // Render upcoming tasks
    renderTasksList('upcomingTasks', upcomingTasks);
}

function renderTasksList(containerId, tasksList) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (tasksList.length === 0) {
        container.innerHTML = '<p class="no-tasks">Žiadne úlohy</p>';
        return;
    }

    const tasksHTML = tasksList.map(task => {
        const order = orders.find(o => o.id === task.orderId);
        const customerName = order ? order.customer.name : 'Neznámy zákazník';
        const location = order ? getLocationName(order.location) : 'Neznáma lokalita';

        return `
            <div class="task-item ${task.status === 'completed' ? 'completed' : ''}">
                <input type="checkbox" class="task-checkbox"
                       ${task.status === 'completed' ? 'checked' : ''}
                       onchange="toggleTaskStatus('${task.id}')">
                <div class="task-content">
                    <div class="task-title">${customerName} - ${getPackageName(order?.package)}</div>
                    <div class="task-details">
                        <div class="task-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            ${location}
                        </div>
                        <div class="task-detail">
                            <i class="fas fa-clipboard-list"></i>
                            ${task.description}
                        </div>
                        <div class="task-detail">
                            <i class="fas fa-clock"></i>
                            ${formatDate(task.date)}
                        </div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-secondary" onclick="showTaskDetail('${task.id}')">
                        <i class="fas fa-info"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="editTask('${task.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = tasksHTML;
}

function getLocationName(locationCode) {
    const locations = {
        'bratislava': 'Cintorín Bratislava',
        'petrzalka': 'Cintorín Petržalka'
    };
    return locations[locationCode] || locationCode;
}

function getPackageName(packageCode) {
    const packageDef = packageDefinitions[packageCode];
    return packageDef ? packageDef.name : packageCode;
}

function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('sk-SK', {
        day: 'numeric',
        month: 'short'
    });
}

function toggleTaskStatus(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = task.status === 'completed' ? 'pending' : 'completed';
        if (task.status === 'completed') {
            task.completedAt = new Date().toISOString();
        } else {
            delete task.completedAt;
        }

        saveOrdersData();
        updateDashboard();
        renderTasks();
    }
}

function showTaskDetail(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) return;

    const detailHTML = `
        <div class="task-detail-content">
            <h4>${order.customer.name} - ${getPackageName(order.package)}</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Dátum:</strong> ${new Date(task.date).toLocaleDateString('sk-SK')}
                </div>
                <div class="detail-item">
                    <strong>Typ:</strong> ${task.description}
                </div>
                <div class="detail-item">
                    <strong>Lokalita:</strong> ${getLocationName(order.location)}
                </div>
                <div class="detail-item">
                    <strong>Status:</strong> ${task.status === 'completed' ? 'Dokončené' : 'Čakajúce'}
                </div>
                <div class="detail-item">
                    <strong>Zákazník:</strong> ${order.customer.name}
                </div>
                <div class="detail-item">
                    <strong>Telefón:</strong> ${order.customer.phone}
                </div>
                <div class="detail-item">
                    <strong>Email:</strong> ${order.customer.email}
                </div>
            </div>
            ${task.notes ? `<div class="task-notes"><strong>Poznámky:</strong><br>${task.notes}</div>` : ''}
        </div>
    `;

    document.getElementById('taskDetailContent').innerHTML = detailHTML;
    showModal('taskDetailModal');
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    modal.style.display = 'flex';
}

function filterTasks() {
    const searchTerm = document.getElementById('searchTasks')?.value.toLowerCase() || '';
    const dateFilter = document.getElementById('dateFilter')?.value || 'all';
    const locationFilter = document.getElementById('locationFilter')?.value || 'all';
    const typeFilter = document.getElementById('typeFilter')?.value || 'all';

    let filteredTasks = tasks.filter(task => {
        const order = orders.find(o => o.id === task.orderId);
        if (!order) return false;

        // Search filter
        if (searchTerm && !order.customer.name.toLowerCase().includes(searchTerm)) {
            return false;
        }

        // Location filter
        if (locationFilter !== 'all' && order.location !== locationFilter) {
            return false;
        }

        // Type filter
        if (typeFilter !== 'all') {
            if (typeFilter === 'basic' && !task.description.toLowerCase().includes('základná')) {
                return false;
            }
            if (typeFilter === 'deep' && !task.description.toLowerCase().includes('hĺbková')) {
                return false;
            }
        }

        // Date filter
        const taskDate = new Date(task.date);
        const today = new Date();

        switch (dateFilter) {
            case 'today':
                return taskDate.toDateString() === today.toDateString();
            case 'week':
                const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
                return taskDate >= today && taskDate <= weekFromNow;
            case 'month':
                return taskDate.getMonth() === today.getMonth() &&
                       taskDate.getFullYear() === today.getFullYear();
            default:
                return true;
        }
    });

    // Re-render with filtered tasks
    const todayTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        return taskDate.toDateString() === today.toDateString();
    });

    const upcomingTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        const diffTime = taskDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 && diffDays <= 7;
    });

    renderTasksList('todayTasks', todayTasks);
    renderTasksList('upcomingTasks', upcomingTasks);
}

function showCalendarView() {
    generateCalendar();
    showModal('calendarModal');
}

function generateCalendar() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Update calendar title
    const monthNames = [
        'Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún',
        'Júl', 'August', 'September', 'Október', 'November', 'December'
    ];
    document.getElementById('calendarTitle').textContent = `${monthNames[month]} ${year}`;

    // Generate calendar grid
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday

    const calendarHTML = [];

    // Add day headers
    const dayHeaders = ['Po', 'Ut', 'St', 'Št', 'Pi', 'So', 'Ne'];
    dayHeaders.forEach(day => {
        calendarHTML.push(`<div class="calendar-day-header">${day}</div>`);
    });

    // Add calendar days
    const currentDate = new Date(startDate);
    for (let i = 0; i < 42; i++) { // 6 weeks
        const isCurrentMonth = currentDate.getMonth() === month;
        const isToday = currentDate.toDateString() === new Date().toDateString();
        const dateStr = currentDate.toISOString().split('T')[0];

        // Find tasks for this day
        const dayTasks = tasks.filter(task => task.date === dateStr);

        let dayClass = 'calendar-day';
        if (!isCurrentMonth) dayClass += ' other-month';
        if (isToday) dayClass += ' today';
        if (dayTasks.length > 0) dayClass += ' has-tasks';

        const tasksHTML = dayTasks.slice(0, 3).map(task => {
            const order = orders.find(o => o.id === task.orderId);
            const customerName = order ? order.customer.name.split(' ')[0] : 'Neznámy';
            return `<div class="calendar-task">${customerName}</div>`;
        }).join('');

        calendarHTML.push(`
            <div class="${dayClass}">
                <div class="calendar-day-number">${currentDate.getDate()}</div>
                ${tasksHTML}
                ${dayTasks.length > 3 ? `<div class="calendar-task">+${dayTasks.length - 3}</div>` : ''}
            </div>
        `);

        currentDate.setDate(currentDate.getDate() + 1);
    }

    document.getElementById('calendarGrid').innerHTML = calendarHTML.join('');
}

function previousMonth() {
    currentDate.setMonth(currentDate.getMonth() - 1);
    generateCalendar();
}

function nextMonth() {
    currentDate.setMonth(currentDate.getMonth() + 1);
    generateCalendar();
}
